{"name": "imponeer/extensions-setup-contracts", "description": "Interfaces for writing setup logic when installing extensions", "type": "library", "require": {"php": ">=8.3", "imponeer/extension-info-contracts": "^0.5", "imponeer/log-data-output-decorator": "^3.0"}, "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "autoload": {"psr-4": {"Imponeer\\Contracts\\ExtensionsSetup\\": "src/"}}, "keywords": ["setup", "contracts", "extensions", "abstractions", "decoupling", "interfaces"], "require-dev": {"squizlabs/php_codesniffer": "^3.9", "phpstan/phpstan": "^2.0"}, "conflict": {"imponeer/extensions-setup-contracts": "<0.2"}, "scripts": {"phpcs": "phpcs", "phpcbf": "phpcbf", "phpstan": "phpstan"}}