{"name": "imponeer/extensions-setup-contracts", "description": "Interfaces for writing setup logic when installing extensions", "type": "library", "require": {"imponeer/log-data-output-decorator": "^1.0 || ^3.0", "php": ">=8.3", "imponeer/extension-info-contracts": "^0.1.0 || ^0.3 || ^0.5"}, "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "autoload": {"psr-4": {"Imponeer\\Contracts\\ExtensionsSetup\\": "src/"}}, "keywords": ["setup", "contracts", "extensions", "abstractions", "decoupling", "interfaces"], "require-dev": {"squizlabs/php_codesniffer": "^3.9", "phpstan/phpstan": "^2.0"}, "scripts": {"phpcs": "phpcs", "phpcbf": "phpcbf", "phpstan": "phpstan"}}